/**
 * 多格式导出工具
 * 支持Excel、Word、PDF格式的PDCA分析报告导出
 */

import { message } from 'ant-design-vue'

// 导出格式枚举
export enum ExportFormat {
  EXCEL = 'excel',
  WORD = 'word',
  PDF = 'pdf'
}

// 导出数据接口
export interface ExportData {
  eventInfo: {
    eventId: string
    eventType: string
    eventTitle: string
    reportDate: string
    reporter: string
    department: string
  }
  pdcaData: {
    plan: {
      problemDescription: string
      rootCause: string
      targetGoal: string
      actionPlan: string
      timeline: string
      responsible: string
      resources: string
      riskAssessment: string
    }
    do: {
      implementationSteps: string[]
      actualActions: string
      timeline: string
      responsible: string
      resourcesUsed: string
      challenges: string
    }
    check: {
      monitoringMethod: string
      dataCollection: string
      results: string
      analysis: string
      timeline: string
      responsible: string
      deviations: string
    }
    act: {
      standardization: string
      improvements: string
      nextCycle: string
      lessons: string
      timeline: string
      responsible: string
      followUp: string
    }
  }
  metadata: {
    analysisDate: string
    analyst: string
    reviewDate: string
    reviewer: string
    version: string
    status: string
  }
}

// 文件命名工具
export class FileNameGenerator {
  static generate(
    eventType: string = '患者跌倒',
    eventId: string = '',
    format: ExportFormat,
    version: string = 'V1.0'
  ): string {
    const timestamp = new Date().toISOString().split('T')[0]
    const extension = this.getExtension(format)
    return `PDCA分析报告_${eventType}_${eventId}_${timestamp}_${version}.${extension}`
  }

  private static getExtension(format: ExportFormat): string {
    switch (format) {
      case ExportFormat.EXCEL:
        return 'xlsx'
      case ExportFormat.WORD:
        return 'docx'
      case ExportFormat.PDF:
        return 'pdf'
      default:
        return 'xlsx'
    }
  }
}

// Excel导出器
export class ExcelExporter {
  static async export(data: ExportData, filename: string): Promise<void> {
    console.warn('🚫 ExcelExporter.export 被调用但已禁用:', filename)
    console.log('🔍 调用栈:', new Error().stack)
    return

    try {
      const excelData = this.createExcelData(data)
      const { utils, writeFile } = await import('xlsx')
      const worksheet = utils.aoa_to_sheet(excelData)

      const shortSheetName = 'PDCA分析报告'
      const workbook = {
        SheetNames: [shortSheetName],
        Sheets: {
          [shortSheetName]: worksheet,
        },
      }

      writeFile(workbook, filename, { bookType: 'xlsx' })
      message.success('Excel格式导出成功')
    } catch (error) {
      console.error('Excel导出失败:', error)
      throw new Error('Excel导出失败')
    }
  }

  private static createExcelData(data: ExportData): any[][] {
    const excelData = []

    // 添加报告头部信息
    excelData.push(['PDCA分析报告'])
    excelData.push(['事件编号', data.eventInfo.eventId])
    excelData.push(['事件类型', data.eventInfo.eventType])
    excelData.push(['事件标题', data.eventInfo.eventTitle])
    excelData.push(['报告日期', data.eventInfo.reportDate])
    excelData.push(['报告人', data.eventInfo.reporter])
    excelData.push(['科室', data.eventInfo.department])
    excelData.push([]) // 空行

    // Plan阶段
    excelData.push(['Plan (计划阶段)'])
    excelData.push(['问题描述', data.pdcaData.plan.problemDescription])
    excelData.push(['根本原因', data.pdcaData.plan.rootCause])
    excelData.push(['目标设定', data.pdcaData.plan.targetGoal])
    excelData.push(['行动计划', data.pdcaData.plan.actionPlan])
    excelData.push(['时间安排', data.pdcaData.plan.timeline])
    excelData.push(['负责人', data.pdcaData.plan.responsible])
    excelData.push(['所需资源', data.pdcaData.plan.resources])
    excelData.push(['风险评估', data.pdcaData.plan.riskAssessment])
    excelData.push([]) // 空行

    // Do阶段
    excelData.push(['Do (执行阶段)'])
    excelData.push(['实施步骤', data.pdcaData.do.implementationSteps.join('; ')])
    excelData.push(['实际行动', data.pdcaData.do.actualActions])
    excelData.push(['时间安排', data.pdcaData.do.timeline])
    excelData.push(['负责人', data.pdcaData.do.responsible])
    excelData.push(['使用资源', data.pdcaData.do.resourcesUsed])
    excelData.push(['遇到挑战', data.pdcaData.do.challenges])
    excelData.push([]) // 空行

    // Check阶段
    excelData.push(['Check (检查阶段)'])
    excelData.push(['监控方法', data.pdcaData.check.monitoringMethod])
    excelData.push(['数据收集', data.pdcaData.check.dataCollection])
    excelData.push(['检查结果', data.pdcaData.check.results])
    excelData.push(['结果分析', data.pdcaData.check.analysis])
    excelData.push(['时间安排', data.pdcaData.check.timeline])
    excelData.push(['负责人', data.pdcaData.check.responsible])
    excelData.push(['偏差情况', data.pdcaData.check.deviations])
    excelData.push([]) // 空行

    // Act阶段
    excelData.push(['Act (行动阶段)'])
    excelData.push(['标准化措施', data.pdcaData.act.standardization])
    excelData.push(['改进措施', data.pdcaData.act.improvements])
    excelData.push(['下一循环', data.pdcaData.act.nextCycle])
    excelData.push(['经验教训', data.pdcaData.act.lessons])
    excelData.push(['时间安排', data.pdcaData.act.timeline])
    excelData.push(['负责人', data.pdcaData.act.responsible])
    excelData.push(['后续跟进', data.pdcaData.act.followUp])
    excelData.push([]) // 空行

    // 添加元数据
    excelData.push(['元数据信息'])
    excelData.push(['分析日期', data.metadata.analysisDate])
    excelData.push(['分析师', data.metadata.analyst])
    excelData.push(['审核日期', data.metadata.reviewDate])
    excelData.push(['审核人', data.metadata.reviewer])
    excelData.push(['版本', data.metadata.version])
    excelData.push(['状态', data.metadata.status])

    return excelData
  }
}

// Word导出器
export class WordExporter {
  static async export(data: ExportData, filename: string): Promise<void> {
    console.warn('🚫 WordExporter.export 被调用但已禁用:', filename)
    console.log('🔍 调用栈:', new Error().stack)
    return

    try {
      const { Document, Packer } = await import('docx')

      const doc = new Document({
        sections: [{
          properties: {},
          children: await this.createWordContent(data)
        }]
      })

      const blob = await Packer.toBlob(doc)
      this.downloadBlob(blob, filename)
      message.success('Word格式导出成功')
    } catch (error) {
      console.error('Word导出失败:', error)
      throw new Error('Word导出失败')
    }
  }

  private static async createWordContent(data: ExportData): Promise<any[]> {
    const { Paragraph, TextRun, HeadingLevel, AlignmentType } = await import('docx')
    const content = []

    // 标题
    content.push(
      new Paragraph({
        children: [new TextRun({ text: 'PDCA分析报告', bold: true, size: 32 })],
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER
      })
    )

    // 事件信息
    content.push(
      new Paragraph({
        children: [new TextRun({ text: '事件信息', bold: true, size: 24 })],
        heading: HeadingLevel.HEADING_1
      })
    )

    const eventInfo = [
      ['事件编号', data.eventInfo.eventId],
      ['事件类型', data.eventInfo.eventType],
      ['事件标题', data.eventInfo.eventTitle],
      ['报告日期', data.eventInfo.reportDate],
      ['报告人', data.eventInfo.reporter],
      ['科室', data.eventInfo.department]
    ]

    eventInfo.forEach(([label, value]) => {
      content.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${label}：`, bold: true }),
            new TextRun({ text: value })
          ]
        })
      )
    })

    // Plan阶段
    content.push(
      new Paragraph({
        children: [new TextRun({ text: 'Plan (计划阶段)', bold: true, size: 20 })],
        heading: HeadingLevel.HEADING_2
      })
    )

    const planInfo = [
      ['问题描述', data.pdcaData.plan.problemDescription],
      ['根本原因', data.pdcaData.plan.rootCause],
      ['目标设定', data.pdcaData.plan.targetGoal],
      ['行动计划', data.pdcaData.plan.actionPlan],
      ['时间安排', data.pdcaData.plan.timeline],
      ['负责人', data.pdcaData.plan.responsible],
      ['所需资源', data.pdcaData.plan.resources],
      ['风险评估', data.pdcaData.plan.riskAssessment]
    ]

    planInfo.forEach(([label, value]) => {
      content.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${label}：`, bold: true }),
            new TextRun({ text: value })
          ]
        })
      )
    })

    // Do阶段
    content.push(
      new Paragraph({
        children: [new TextRun({ text: 'Do (执行阶段)', bold: true, size: 20 })],
        heading: HeadingLevel.HEADING_2
      })
    )

    const doInfo = [
      ['实施步骤', data.pdcaData.do.implementationSteps.join('; ')],
      ['实际行动', data.pdcaData.do.actualActions],
      ['时间安排', data.pdcaData.do.timeline],
      ['负责人', data.pdcaData.do.responsible],
      ['使用资源', data.pdcaData.do.resourcesUsed],
      ['遇到挑战', data.pdcaData.do.challenges]
    ]

    doInfo.forEach(([label, value]) => {
      content.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${label}：`, bold: true }),
            new TextRun({ text: value })
          ]
        })
      )
    })

    // Check阶段
    content.push(
      new Paragraph({
        children: [new TextRun({ text: 'Check (检查阶段)', bold: true, size: 20 })],
        heading: HeadingLevel.HEADING_2
      })
    )

    const checkInfo = [
      ['监控方法', data.pdcaData.check.monitoringMethod],
      ['数据收集', data.pdcaData.check.dataCollection],
      ['检查结果', data.pdcaData.check.results],
      ['结果分析', data.pdcaData.check.analysis],
      ['时间安排', data.pdcaData.check.timeline],
      ['负责人', data.pdcaData.check.responsible],
      ['偏差情况', data.pdcaData.check.deviations]
    ]

    checkInfo.forEach(([label, value]) => {
      content.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${label}：`, bold: true }),
            new TextRun({ text: value })
          ]
        })
      )
    })

    // Act阶段
    content.push(
      new Paragraph({
        children: [new TextRun({ text: 'Act (行动阶段)', bold: true, size: 20 })],
        heading: HeadingLevel.HEADING_2
      })
    )

    const actInfo = [
      ['标准化措施', data.pdcaData.act.standardization],
      ['改进措施', data.pdcaData.act.improvements],
      ['下一循环', data.pdcaData.act.nextCycle],
      ['经验教训', data.pdcaData.act.lessons],
      ['时间安排', data.pdcaData.act.timeline],
      ['负责人', data.pdcaData.act.responsible],
      ['后续跟进', data.pdcaData.act.followUp]
    ]

    actInfo.forEach(([label, value]) => {
      content.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${label}：`, bold: true }),
            new TextRun({ text: value })
          ]
        })
      )
    })

    // 元数据信息
    content.push(
      new Paragraph({
        children: [new TextRun({ text: '元数据信息', bold: true, size: 20 })],
        heading: HeadingLevel.HEADING_2
      })
    )

    const metadataInfo = [
      ['分析日期', data.metadata.analysisDate],
      ['分析师', data.metadata.analyst],
      ['审核日期', data.metadata.reviewDate],
      ['审核人', data.metadata.reviewer],
      ['版本', data.metadata.version],
      ['状态', data.metadata.status]
    ]

    metadataInfo.forEach(([label, value]) => {
      content.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${label}：`, bold: true }),
            new TextRun({ text: value })
          ]
        })
      )
    })

    return content
  }

  private static downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

// PDF导出器
export class PDFExporter {
  static async export(data: ExportData, filename: string): Promise<void> {
    console.warn('🚫 PDFExporter.export 被调用但已禁用:', filename)
    console.log('🔍 调用栈:', new Error().stack)
    return

    try {
      const { jsPDF } = await import('jspdf')
      const pdf = new jsPDF('p', 'mm', 'a4')

      // 设置中文字体支持
      pdf.setFont('helvetica')

      let yPosition = 20
      const lineHeight = 8
      const pageHeight = pdf.internal.pageSize.height

      // 添加标题
      pdf.setFontSize(20)
      pdf.text('PDCA Analysis Report', 105, yPosition, { align: 'center' })
      yPosition += lineHeight * 2

      // 添加事件信息
      pdf.setFontSize(12)
      const eventInfo = [
        ['Event ID:', data.eventInfo.eventId],
        ['Event Type:', data.eventInfo.eventType],
        ['Event Title:', data.eventInfo.eventTitle],
        ['Report Date:', data.eventInfo.reportDate],
        ['Reporter:', data.eventInfo.reporter],
        ['Department:', data.eventInfo.department]
      ]

      eventInfo.forEach(([label, value]) => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(`${label} ${value}`, 20, yPosition)
        yPosition += lineHeight
      })

      yPosition += lineHeight

      // Plan阶段
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = 20
      }
      pdf.setFontSize(14)
      pdf.text('Plan (Planning Phase)', 20, yPosition)
      yPosition += lineHeight

      pdf.setFontSize(10)
      const planInfo = [
        `Problem Description: ${data.pdcaData.plan.problemDescription}`,
        `Root Cause: ${data.pdcaData.plan.rootCause}`,
        `Target Goal: ${data.pdcaData.plan.targetGoal}`,
        `Action Plan: ${data.pdcaData.plan.actionPlan}`,
        `Timeline: ${data.pdcaData.plan.timeline}`,
        `Responsible: ${data.pdcaData.plan.responsible}`,
        `Resources: ${data.pdcaData.plan.resources}`,
        `Risk Assessment: ${data.pdcaData.plan.riskAssessment}`
      ]

      planInfo.forEach((info) => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(info, 25, yPosition)
        yPosition += lineHeight
      })

      yPosition += lineHeight

      // Do阶段
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = 20
      }
      pdf.setFontSize(14)
      pdf.text('Do (Implementation Phase)', 20, yPosition)
      yPosition += lineHeight

      pdf.setFontSize(10)
      const doInfo = [
        `Implementation Steps: ${data.pdcaData.do.implementationSteps.join('; ')}`,
        `Actual Actions: ${data.pdcaData.do.actualActions}`,
        `Timeline: ${data.pdcaData.do.timeline}`,
        `Responsible: ${data.pdcaData.do.responsible}`,
        `Resources Used: ${data.pdcaData.do.resourcesUsed}`,
        `Challenges: ${data.pdcaData.do.challenges}`
      ]

      doInfo.forEach((info) => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(info, 25, yPosition)
        yPosition += lineHeight
      })

      yPosition += lineHeight

      // Check阶段
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = 20
      }
      pdf.setFontSize(14)
      pdf.text('Check (Evaluation Phase)', 20, yPosition)
      yPosition += lineHeight

      pdf.setFontSize(10)
      const checkInfo = [
        `Monitoring Method: ${data.pdcaData.check.monitoringMethod}`,
        `Data Collection: ${data.pdcaData.check.dataCollection}`,
        `Results: ${data.pdcaData.check.results}`,
        `Analysis: ${data.pdcaData.check.analysis}`,
        `Timeline: ${data.pdcaData.check.timeline}`,
        `Responsible: ${data.pdcaData.check.responsible}`,
        `Deviations: ${data.pdcaData.check.deviations}`
      ]

      checkInfo.forEach((info) => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(info, 25, yPosition)
        yPosition += lineHeight
      })

      yPosition += lineHeight

      // Act阶段
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = 20
      }
      pdf.setFontSize(14)
      pdf.text('Act (Action Phase)', 20, yPosition)
      yPosition += lineHeight

      pdf.setFontSize(10)
      const actInfo = [
        `Standardization: ${data.pdcaData.act.standardization}`,
        `Improvements: ${data.pdcaData.act.improvements}`,
        `Next Cycle: ${data.pdcaData.act.nextCycle}`,
        `Lessons: ${data.pdcaData.act.lessons}`,
        `Timeline: ${data.pdcaData.act.timeline}`,
        `Responsible: ${data.pdcaData.act.responsible}`,
        `Follow Up: ${data.pdcaData.act.followUp}`
      ]

      actInfo.forEach((info) => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(info, 25, yPosition)
        yPosition += lineHeight
      })

      yPosition += lineHeight

      // 元数据信息
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = 20
      }
      pdf.setFontSize(14)
      pdf.text('Metadata', 20, yPosition)
      yPosition += lineHeight

      pdf.setFontSize(10)
      const metadataInfo = [
        `Analysis Date: ${data.metadata.analysisDate}`,
        `Analyst: ${data.metadata.analyst}`,
        `Review Date: ${data.metadata.reviewDate}`,
        `Reviewer: ${data.metadata.reviewer}`,
        `Version: ${data.metadata.version}`,
        `Status: ${data.metadata.status}`
      ]

      metadataInfo.forEach((info) => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(info, 25, yPosition)
        yPosition += lineHeight
      })

      pdf.save(filename)
      message.success('PDF格式导出成功')
    } catch (error) {
      console.error('PDF导出失败:', error)
      throw new Error('PDF导出失败')
    }
  }
}

// 全局导出控制开关
let exportEnabled = false

// 启用导出功能（只有用户明确点击时才启用）
export function enableExport() {
  exportEnabled = true
  console.log('🔓 导出功能已启用')
}

// 禁用导出功能
export function disableExport() {
  exportEnabled = false
  console.log('🔒 导出功能已禁用')
}

// 多格式导出管理器
export class MultiFormatExporter {
  static async export(data: ExportData, format: ExportFormat, eventType: string = '患者跌倒'): Promise<void> {
    // 检查导出是否被启用
    if (!exportEnabled) {
      console.warn(`🚫 导出功能已禁用，阻止${format}格式导出`)
      console.log(`🔍 调用栈追踪:`, new Error().stack)
      return
    }

    const filename = FileNameGenerator.generate(eventType, data.eventInfo.eventId, format, data.metadata.version)

    // 添加调用栈追踪，找出是谁在自动调用导出
    console.log(`🔍 MultiFormatExporter: 准备导出格式 ${format}, 文件名: ${filename}`)
    console.log(`🔍 调用栈追踪:`, new Error().stack)

    switch (format) {
      case ExportFormat.EXCEL:
        console.log('📊 调用 ExcelExporter.export')
        await ExcelExporter.export(data, filename)
        break
      case ExportFormat.WORD:
        console.log('📄 调用 WordExporter.export')
        await WordExporter.export(data, filename)
        break
      case ExportFormat.PDF:
        console.log('📋 调用 PDFExporter.export')
        await PDFExporter.export(data, filename)
        break
      default:
        throw new Error(`不支持的导出格式: ${format}`)
    }
  }
}
