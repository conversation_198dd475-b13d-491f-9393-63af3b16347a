<template>
  <div class="multi-format-export-test">
    <PageWrapper title="多格式导出功能测试" content-full-height>
      <!-- 测试说明 -->
      <div class="test-info">
        <a-alert
          message="多格式导出功能测试"
          description="此页面用于测试PDCA分析报告的Excel、Word、PDF三种格式导出功能"
          type="info"
          show-icon
          style="margin-bottom: 24px;"
        />
      </div>

      <!-- 测试数据配置 -->
      <a-card title="测试数据配置" style="margin-bottom: 24px;">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="事件编号">
                <a-input v-model:value="testData.eventId" placeholder="EVT2025070200000178" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="事件类型">
                <a-input v-model:value="testData.eventType" placeholder="患者跌倒" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="分析日期">
                <a-date-picker v-model:value="testData.analysisDate" style="width: 100%;" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 导出测试区域 -->
      <a-card title="导出功能测试">
        <div class="export-controls">
          <a-space size="large">
            <a-button 
              type="primary" 
              :icon="h(FileExcelOutlined)"
              @click="testExport('excel')"
              :loading="exportLoading.excel"
            >
              测试Excel导出
            </a-button>
            
            <a-button 
              type="primary" 
              :icon="h(FileWordOutlined)"
              @click="testExport('word')"
              :loading="exportLoading.word"
            >
              测试Word导出
            </a-button>
            
            <a-button 
              type="primary" 
              :icon="h(FilePdfOutlined)"
              @click="testExport('pdf')"
              :loading="exportLoading.pdf"
            >
              测试PDF导出
            </a-button>
          </a-space>
        </div>

        <!-- 导出结果显示 -->
        <div v-if="exportResults.length > 0" class="export-results" style="margin-top: 24px;">
          <h4>导出结果:</h4>
          <a-list :data-source="exportResults" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span :class="item.success ? 'success-text' : 'error-text'">
                      {{ item.format.toUpperCase() }}导出 - {{ item.success ? '成功' : '失败' }}
                    </span>
                  </template>
                  <template #description>
                    <div>
                      <div>文件名: {{ item.filename }}</div>
                      <div>时间: {{ item.timestamp }}</div>
                      <div v-if="item.error" class="error-text">错误: {{ item.error }}</div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-card>

      <!-- PDCA测试数据预览 -->
      <a-card title="PDCA测试数据预览" style="margin-top: 24px;">
        <pre>{{ JSON.stringify(mockPDCAData, null, 2) }}</pre>
      </a-card>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { PageWrapper } from '@/components/Page'
import { 
  FileExcelOutlined, 
  FileWordOutlined, 
  FilePdfOutlined 
} from '@ant-design/icons-vue'

// 导入多格式导出工具
import { MultiFormatExporter, ExportFormat, type ExportData } from '../utils/export/multiFormatExporter'

// 测试数据
const testData = ref({
  eventId: 'EVT2025070200000178',
  eventType: '患者跌倒',
  analysisDate: dayjs()
})

// 导出加载状态
const exportLoading = ref({
  excel: false,
  word: false,
  pdf: false
})

// 导出结果
const exportResults = ref<Array<{
  format: string
  success: boolean
  filename: string
  timestamp: string
  error?: string
}>>([])

// 模拟PDCA数据
const mockPDCAData = ref<ExportData>({
  eventInfo: {
    eventId: 'EVT2025070200000178',
    eventType: '患者跌倒',
    eventTitle: '患者跌倒事件分析',
    reportDate: '2025-08-03',
    reporter: '张护士',
    department: '内科病房'
  },
  pdcaData: {
    plan: {
      problemDescription: '患者在病房内发生跌倒事件，需要分析原因并制定改进措施',
      rootCause: '地面湿滑、患者行动不便、护理人员巡视不及时',
      targetGoal: '降低患者跌倒发生率至0.5%以下',
      actionPlan: '加强地面清洁、增加防滑措施、提高巡视频率',
      timeline: '2025年8月-10月',
      responsible: '护理部、后勤部',
      resources: '防滑垫、警示标识、培训资源',
      riskAssessment: '中等风险，需要持续监控'
    },
    do: {
      implementationSteps: [
        '购买并安装防滑垫',
        '制作并张贴警示标识',
        '组织护理人员培训',
        '建立巡视记录制度'
      ],
      actualActions: '已完成防滑垫安装，正在进行人员培训',
      timeline: '2025年8月1日-8月15日',
      responsible: '护理部主任、病房护士长',
      resourcesUsed: '防滑垫50个、警示标识20个、培训费用5000元',
      challenges: '部分护理人员对新制度适应较慢'
    },
    check: {
      monitoringMethod: '每日巡查、事件统计、患者反馈',
      dataCollection: '跌倒事件记录表、巡视记录、患者满意度调查',
      results: '跌倒事件减少60%，患者满意度提升',
      analysis: '防滑措施效果显著，但仍需加强夜间巡视',
      timeline: '2025年8月16日-8月31日',
      responsible: '质控护士、病房护士长',
      deviations: '夜间巡视频率仍需提高'
    },
    act: {
      standardization: '将有效措施纳入标准操作流程',
      improvements: '增加夜间巡视频率，完善应急预案',
      nextCycle: '继续监控并优化防跌倒措施',
      lessons: '预防措施的及时实施对降低风险至关重要',
      timeline: '2025年9月1日起持续执行',
      responsible: '护理部、各病房护士长',
      followUp: '每月评估一次，季度总结改进'
    }
  },
  metadata: {
    analysisDate: '2025-08-03',
    analyst: '质控护士',
    reviewDate: '2025-08-10',
    reviewer: '护理部主任',
    version: 'V1.0',
    status: '已完成'
  }
})

// 测试导出功能
const testExport = async (format: 'excel' | 'word' | 'pdf') => {
  try {
    exportLoading.value[format] = true

    // 导入导出控制函数
    const { enableExport, disableExport } = await import('@/utils/export/multiFormatExporter')

    const exportFormat = format === 'excel' ? ExportFormat.EXCEL :
                        format === 'word' ? ExportFormat.WORD :
                        ExportFormat.PDF
    
    // 更新测试数据
    const exportData = {
      ...mockPDCAData.value,
      eventInfo: {
        ...mockPDCAData.value.eventInfo,
        eventId: testData.value.eventId,
        eventType: testData.value.eventType
      },
      metadata: {
        ...mockPDCAData.value.metadata,
        analysisDate: testData.value.analysisDate.format('YYYY-MM-DD')
      }
    }

    // 启用导出功能
    enableExport()

    // 执行导出
    await MultiFormatExporter.export(exportData, exportFormat, testData.value.eventType)

    // 禁用导出功能
    disableExport()
    
    // 记录成功结果
    const filename = `PDCA分析报告_${testData.value.eventType}_${testData.value.eventId}_${testData.value.analysisDate.format('YYYY-MM-DD')}_V1.0.${format === 'excel' ? 'xlsx' : format === 'word' ? 'docx' : 'pdf'}`
    
    exportResults.value.unshift({
      format,
      success: true,
      filename,
      timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
    })
    
    message.success(`${format.toUpperCase()}格式导出成功！`)
    
  } catch (error) {
    console.error(`${format}导出失败:`, error)
    
    // 记录失败结果
    exportResults.value.unshift({
      format,
      success: false,
      filename: '导出失败',
      timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      error: error instanceof Error ? error.message : String(error)
    })
    
    message.error(`${format.toUpperCase()}格式导出失败: ${error instanceof Error ? error.message : String(error)}`)
  } finally {
    exportLoading.value[format] = false
  }
}
</script>

<style scoped lang="less">
.multi-format-export-test {
  .export-controls {
    text-align: center;
    padding: 24px 0;
  }
  
  .success-text {
    color: #52c41a;
  }
  
  .error-text {
    color: #ff4d4f;
  }
  
  pre {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 6px;
    font-size: 12px;
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>
